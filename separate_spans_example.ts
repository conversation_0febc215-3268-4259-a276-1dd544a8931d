#!/usr/bin/env tsx

/**
 * Example showing how to create completely separate spans and establish 
 * parent-child relationships using the parent parameter instead of calling
 * parentSpan.startSpan().
 * 
 * This demonstrates three different approaches:
 * 1. Using span.export() and parent parameter
 * 2. Using parentSpanIds parameter directly
 * 3. Using startSpanWithParents for multiple parents
 * 
 * Usage:
 *     npx tsx separate_spans_example.ts
 */

import { initLogger } from "braintrust";

async function main() {
  const logger = initLogger({ projectName: "pedro-repro4667" });
  
  console.log("=== Method 1: Using export() and parent parameter ===");
  
  // Create first span completely independently
  const parentSpan = logger.startSpan({
    name: "parent_task",
    spanId: "parent-001",
    event: {
      input: "Starting parent task",
      metadata: { task_type: "parent" }
    }
  });
  
  // Complete the parent span
  parentSpan.log({
    output: "Parent task completed",
    scores: { success: 1.0 }
  });
  parentSpan.end();
  
  // Export the parent span to get its reference
  const parentExport = await parentSpan.export();
  console.log(`Parent span export: ${parentExport}`);
  
  // Create a completely separate child span using the parent export
  const childSpan = logger.startSpan({
    name: "child_task",
    spanId: "child-001", 
    parent: parentExport, // This establishes the parent-child relationship
    event: {
      input: "Starting child task",
      metadata: { task_type: "child", parent_id: "parent-001" }
    }
  });
  
  childSpan.log({
    output: "Child task completed",
    scores: { success: 1.0 }
  });
  childSpan.end();
  
  console.log(`Child span ID: ${childSpan.spanId}`);
  console.log(`Child root span ID: ${childSpan.rootSpanId}`);
  console.log(`Child span parents: ${JSON.stringify(childSpan.spanParents)}`);
  
  console.log("\n=== Method 2: Using parentSpanIds parameter ===");
  
  // Create another parent span
  const parent2Span = logger.startSpan({
    name: "parent_task_2",
    spanId: "parent-002",
    event: {
      input: "Starting second parent task",
      metadata: { task_type: "parent" }
    }
  });
  
  parent2Span.log({ output: "Second parent completed" });
  parent2Span.end();
  
  // Create child using parentSpanIds directly (without export)
  const child2Span = logger.startSpan({
    name: "child_task_2",
    spanId: "child-002",
    parentSpanIds: {
      spanId: parent2Span.spanId,        // Direct reference to parent span ID
      rootSpanId: parent2Span.rootSpanId // Direct reference to root span ID
    },
    event: {
      input: "Starting second child task",
      metadata: { task_type: "child", parent_id: "parent-002" }
    }
  });
  
  child2Span.log({ output: "Second child completed" });
  child2Span.end();
  
  console.log(`Child2 span ID: ${child2Span.spanId}`);
  console.log(`Child2 root span ID: ${child2Span.rootSpanId}`);
  console.log(`Child2 span parents: ${JSON.stringify(child2Span.spanParents)}`);
  
  console.log("\n=== Method 3: Using startSpanWithParents (multiple parents) ===");
  
  // Create multiple parent spans
  const parentA = logger.startSpan({
    name: "parent_A",
    spanId: "parent-A",
    event: { input: "Parent A task" }
  });
  parentA.log({ output: "Parent A completed" });
  parentA.end();
  
  const parentB = logger.startSpan({
    name: "parent_B", 
    spanId: "parent-B",
    event: { input: "Parent B task" }
  });
  parentB.log({ output: "Parent B completed" });
  parentB.end();
  
  // Create a span with multiple parents using startSpanWithParents
  const multiParentChild = parentA.startSpanWithParents(
    "multi-child-001", // spanId
    [parentA.spanId, parentB.spanId], // array of parent span IDs
    {
      name: "multi_parent_child",
      event: {
        input: "Task with multiple parents",
        metadata: { 
          parent_ids: ["parent-A", "parent-B"],
          task_type: "multi_parent_child"
        }
      }
    }
  );
  
  multiParentChild.log({ 
    output: "Multi-parent child completed",
    scores: { complexity: 0.8 }
  });
  multiParentChild.end();
  
  console.log(`Multi-parent child ID: ${multiParentChild.spanId}`);
  console.log(`Multi-parent child root: ${multiParentChild.rootSpanId}`);
  console.log(`Multi-parent child parents: ${JSON.stringify(multiParentChild.spanParents)}`);
  
  console.log("\n=== Method 4: Chain of separate spans ===");
  
  // Create a chain where each span is created separately but linked
  const step1 = logger.startSpan({
    name: "step_1",
    spanId: "step-001",
    event: { input: "First step" }
  });
  step1.log({ output: "Step 1 completed" });
  step1.end();
  
  const step1Export = await step1.export();
  
  const step2 = logger.startSpan({
    name: "step_2", 
    spanId: "step-002",
    parent: step1Export,
    event: { input: "Second step" }
  });
  step2.log({ output: "Step 2 completed" });
  step2.end();
  
  const step2Export = await step2.export();
  
  const step3 = logger.startSpan({
    name: "step_3",
    spanId: "step-003", 
    parent: step2Export,
    event: { input: "Third step" }
  });
  step3.log({ output: "Step 3 completed" });
  step3.end();
  
  console.log("Created chain: step_1 -> step_2 -> step_3");
  console.log(`Step 3 parents: ${JSON.stringify(step3.spanParents)}`);
  
  // Flush all spans
  await logger.flush();
  console.log("\nAll separate spans with parent-child relationships sent to Braintrust!");
}

main().catch(console.error);
